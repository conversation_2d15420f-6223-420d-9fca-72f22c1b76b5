import React, { useState, useRef, useEffect } from 'react';

interface UserInputProps {
  isProcessing: boolean;
  onSendMessage: (message: string) => void;
  isWorkflowStarted: boolean;
  isWorkflowComplete: boolean;
  lastAiMessage?: string; // Nouveau prop pour détecter les messages "Continuer"
}

export const UserInput: React.FC<UserInputProps> = ({ isProcessing, onSendMessage, isWorkflowStarted, isWorkflowComplete, lastAiMessage }) => {
  const [text, setText] = useState('');
  const textAreaRef = useRef<HTMLTextAreaElement>(null);
  const [showContinueHelper, setShowContinueHelper] = useState(false);

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (text.trim() && !isProcessing) {
      onSendMessage(text);
      setText('');
    }
  };

  useEffect(() => {
    if (textAreaRef.current) {
      textAreaRef.current.style.height = 'auto';
      textAreaRef.current.style.height = `${textAreaRef.current.scrollHeight}px`;
    }
  }, [text]);

  // Détecter si l'IA demande de "Continuer" et proposer une aide
  useEffect(() => {
    if (lastAiMessage && lastAiMessage.toLowerCase().includes('continuer')) {
      setShowContinueHelper(true);
      // Masquer l'aide après 10 secondes
      const timer = setTimeout(() => setShowContinueHelper(false), 10000);
      return () => clearTimeout(timer);
    }
  }, [lastAiMessage]);

  const handleContinueClick = () => {
    setText('Continuer');
    setShowContinueHelper(false);
    // Focus sur le textarea pour que l'utilisateur puisse envoyer immédiatement
    if (textAreaRef.current) {
      textAreaRef.current.focus();
    }
  };

  const getButtonText = () => {
    if (isWorkflowComplete) return 'Terminé';
    if (!isWorkflowStarted) return 'Lancer l\'Analyse';
    return 'Envoyer';
  };
  
  const getPlaceholderText = () => {
      if (isWorkflowComplete) return 'Toutes les étapes sont terminées !';
      if (!isWorkflowStarted) return 'Décrivez votre problème pour commencer...';
      return 'Votre réponse...';
  };

  return (
    <div className="space-y-2">
      {/* Aide contextuelle pour "Continuer" */}
      {showContinueHelper && (
        <div className="bg-amber-900/50 border border-amber-600/50 rounded-lg p-3 flex items-center justify-between">
          <div className="flex items-center gap-2">
            <svg className="w-4 h-4 text-amber-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
            <span className="text-sm text-amber-200">L'IA attend que vous écriviez "Continuer" pour passer à l'étape suivante</span>
          </div>
          <button
            onClick={handleContinueClick}
            className="bg-amber-600 hover:bg-amber-500 text-white text-sm font-semibold py-1 px-3 rounded transition-colors"
          >
            Continuer
          </button>
        </div>
      )}

      <form onSubmit={handleSubmit} className="flex items-end gap-3">
        <textarea
          ref={textAreaRef}
          value={text}
          onChange={(e) => setText(e.target.value)}
          onKeyDown={(e) => {
            if (e.key === 'Enter' && !e.shiftKey) {
              e.preventDefault();
              handleSubmit(e);
            }
          }}
          placeholder={getPlaceholderText()}
          className="flex-grow bg-slate-700 rounded-lg p-3 text-slate-200 placeholder-slate-400 resize-none focus:ring-2 focus:ring-indigo-500 focus:outline-none transition-shadow duration-200 max-h-48"
          rows={1}
          disabled={isProcessing || isWorkflowComplete}
        />
        <button
          type="submit"
          disabled={isProcessing || !text.trim() || isWorkflowComplete}
          className="bg-indigo-600 text-white font-semibold py-3 px-5 rounded-lg hover:bg-indigo-500 disabled:bg-slate-600 disabled:cursor-not-allowed transition-colors duration-200 flex-shrink-0"
        >
          {isProcessing ? '...' : getButtonText()}
        </button>
      </form>
    </div>
  );
};